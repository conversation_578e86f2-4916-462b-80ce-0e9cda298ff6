#!/usr/bin/env python3
"""
Test script for the consolidated API functionality.
Tests both scenarios: with and without uploaded documents.
"""

import requests
import json
import time
import sys
from pathlib import Path

def test_chat_without_uploads(base_url="http://localhost:8000"):
    """Test chat endpoint without any uploaded documents (should return error in PDF-only system)."""
    print("\n🧪 Testing chat endpoint WITHOUT uploaded documents...")

    try:
        response = requests.post(
            f"{base_url}/chat",
            json={"query": "What is this about?"},
            timeout=30
        )

        if response.status_code == 400:
            result = response.json()
            if "No PDF documents have been uploaded" in result.get("detail", ""):
                print("✅ Chat endpoint correctly requires uploaded documents")
                print(f"   Error message: {result['detail']}")
                return True
            else:
                print(f"❌ Unexpected error message: {result.get('detail', 'No detail')}")
                return False
        else:
            print(f"❌ Chat endpoint should return 400 but returned: {response.status_code}")
            print(f"   Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error testing chat without uploads: {e}")
        return False

def test_upload_functionality(base_url="http://localhost:8000"):
    """Test PDF upload functionality."""
    print("\n🧪 Testing PDF upload functionality...")
    
    # Create a simple test PDF content (mock)
    test_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n178\n%%EOF"
    
    try:
        # Try to upload a mock PDF (this will likely fail due to content validation, but tests the endpoint)
        files = [('files', ('test.pdf', test_content, 'application/pdf'))]
        
        response = requests.post(
            f"{base_url}/upload-pdfs",
            files=files,
            timeout=30
        )
        
        if response.status_code in [200, 400]:  # 400 is expected for invalid PDF content
            print("✅ Upload endpoint is accessible")
            if response.status_code == 400:
                print("   (Expected failure due to mock PDF content)")
            return True
        else:
            print(f"❌ Upload endpoint failed unexpectedly: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing upload functionality: {e}")
        return False

def test_chat_with_mock_upload_scenario(base_url="http://localhost:8000"):
    """Test that chat endpoint consistently requires uploaded documents."""
    print("\n🧪 Testing chat endpoint consistency (should always require PDFs)...")

    # Test with a query that would be relevant to uploaded documents
    try:
        response = requests.post(
            f"{base_url}/chat",
            json={"query": "What information is available in the uploaded documents?"},
            timeout=30
        )

        if response.status_code == 400:
            result = response.json()
            if "No PDF documents have been uploaded" in result.get("detail", ""):
                print("✅ Chat endpoint consistently requires uploaded documents")
                print(f"   Error message: {result['detail']}")
                return True
            else:
                print(f"❌ Unexpected error message: {result.get('detail', 'No detail')}")
                return False
        else:
            print(f"❌ Chat endpoint should require PDFs but returned: {response.status_code}")
            print(f"   Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error testing chat consistency: {e}")
        return False

def check_removed_endpoints(base_url="http://localhost:8000"):
    """Verify that the removed endpoints are no longer accessible."""
    print("\n🧪 Verifying removed endpoints are no longer accessible...")
    
    removed_endpoints = [
        ("POST", "/query-uploaded-docs"),
        ("DELETE", "/clear-uploaded-docs"),
        ("GET", "/uploaded-docs-status")
    ]
    
    all_removed = True
    
    for method, endpoint in removed_endpoints:
        try:
            if method == "POST":
                response = requests.post(f"{base_url}{endpoint}", json={"query": "test"}, timeout=10)
            elif method == "DELETE":
                response = requests.delete(f"{base_url}{endpoint}", timeout=10)
            else:  # GET
                response = requests.get(f"{base_url}{endpoint}", timeout=10)
            
            if response.status_code == 404:
                print(f"✅ {method} {endpoint} correctly removed (404)")
            else:
                print(f"❌ {method} {endpoint} still accessible (status: {response.status_code})")
                all_removed = False
                
        except requests.exceptions.RequestException as e:
            print(f"✅ {method} {endpoint} correctly removed (connection error)")
    
    return all_removed

def test_api_documentation(base_url="http://localhost:8000"):
    """Test that API documentation is accessible and updated."""
    print("\n🧪 Testing API documentation accessibility...")
    
    try:
        response = requests.get(f"{base_url}/docs", timeout=10)
        
        if response.status_code == 200:
            print("✅ API documentation is accessible")
            return True
        else:
            print(f"❌ API documentation not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error accessing API documentation: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 Testing PDF-Only Chat System Functionality")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Check if server is running
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code != 200:
            print(f"❌ Server not accessible at {base_url}")
            print("Please start the server first: python main.py")
            return 1
    except requests.exceptions.RequestException:
        print(f"❌ Cannot connect to server at {base_url}")
        print("Please start the server first: python main.py")
        return 1
    
    print(f"✅ Server is running at {base_url}")
    
    # Run all tests
    tests = [
        ("Chat requires PDFs", test_chat_without_uploads),
        ("Upload functionality", test_upload_functionality),
        ("Chat consistency check", test_chat_with_mock_upload_scenario),
        ("Removed endpoints", check_removed_endpoints),
        ("API documentation", test_api_documentation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func(base_url)
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The PDF-only chat system is working correctly.")
        print("\nKey features verified:")
        print("• Chat endpoint correctly requires uploaded documents")
        print("• Upload functionality is accessible")
        print("• Chat endpoint handles upload scenarios")
        print("• Removed endpoints are no longer accessible")
        print("• API documentation is accessible")
        return 0
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    exit(main())
