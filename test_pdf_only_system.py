#!/usr/bin/env python3
"""
Test script to verify the PDF-only system behavior.
Tests that no default PDFs are processed during initialization.
"""

import sys
import os
from pathlib import Path
import tempfile
import shutil

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

def test_no_automatic_pdf_processing():
    """Test that the system doesn't automatically process skuast.pdf during initialization."""
    print("🧪 Testing that no automatic PDF processing occurs...")
    
    # Check if the main.py file has been properly modified
    main_py_path = Path("main.py")
    if not main_py_path.exists():
        print("❌ main.py not found")
        return False
    
    with open(main_py_path, 'r') as f:
        content = f.read()
    
    # Check that process_pdf_if_needed is not called in lifespan (excluding comments)
    lines = content.split('\n')
    for line in lines:
        stripped_line = line.strip()
        if stripped_line.startswith("await process_pdf_if_needed()") and not stripped_line.startswith("#"):
            print("❌ Found uncommented 'await process_pdf_if_needed()' in main.py - automatic PDF processing still enabled")
            return False
    
    # Check that the function is removed or commented out
    if "async def process_pdf_if_needed():" in content and "# Removed:" not in content:
        print("❌ process_pdf_if_needed function still exists and is not commented out")
        return False
    
    print("✅ No automatic PDF processing found in main.py")
    return True

def test_global_variables_updated():
    """Test that global variables have been updated for PDF-only system."""
    print("\n🧪 Testing global variables are updated for PDF-only system...")
    
    main_py_path = Path("main.py")
    with open(main_py_path, 'r') as f:
        content = f.read()
    
    # Check that vector_db and retriever are not initialized globally
    lines = content.split('\n')
    global_vars_section = []
    in_global_vars = False
    
    for line in lines:
        if "# Global variables for components" in line:
            in_global_vars = True
        elif in_global_vars and line.strip() == "":
            break
        elif in_global_vars:
            global_vars_section.append(line)
    
    global_vars_text = '\n'.join(global_vars_section)
    
    # Check that vector_db and retriever are commented out or removed
    if "vector_db: Optional[VectorDatabase] = None" in global_vars_text and "# Note:" not in global_vars_text:
        print("❌ vector_db is still initialized globally")
        return False
    
    if "retriever: Optional[SemanticRetriever] = None" in global_vars_text and "# Note:" not in global_vars_text:
        print("❌ retriever is still initialized globally")
        return False
    
    print("✅ Global variables properly updated for PDF-only system")
    return True

def test_initialization_function_updated():
    """Test that initialize_system function only initializes Gemini client."""
    print("\n🧪 Testing initialize_system function is updated for PDF-only...")
    
    main_py_path = Path("main.py")
    with open(main_py_path, 'r') as f:
        content = f.read()
    
    # Find the initialize_system function
    lines = content.split('\n')
    init_function = []
    in_init_function = False
    
    for line in lines:
        if "async def initialize_system():" in line:
            in_init_function = True
        elif in_init_function and line.startswith("async def ") and "initialize_system" not in line:
            break
        elif in_init_function:
            init_function.append(line)
    
    init_function_text = '\n'.join(init_function)
    
    # Check that vector database initialization is removed
    if "vector_db = VectorDatabase(" in init_function_text:
        print("❌ Vector database is still being initialized in initialize_system")
        return False
    
    # Check that retriever initialization is removed
    if "retriever = SemanticRetriever(" in init_function_text:
        print("❌ Retriever is still being initialized in initialize_system")
        return False
    
    # Check that only Gemini client is initialized
    if "gemini_client = GeminiClient(" not in init_function_text:
        print("❌ Gemini client initialization not found")
        return False
    
    print("✅ initialize_system function properly updated for PDF-only system")
    return True

def test_lifespan_function_updated():
    """Test that lifespan function doesn't call process_pdf_if_needed."""
    print("\n🧪 Testing lifespan function is updated...")
    
    main_py_path = Path("main.py")
    with open(main_py_path, 'r') as f:
        content = f.read()
    
    # Find the lifespan function
    lines = content.split('\n')
    lifespan_function = []
    in_lifespan_function = False
    
    for line in lines:
        if "async def lifespan(" in line:
            in_lifespan_function = True
        elif in_lifespan_function and line.startswith("# Create FastAPI app"):
            break
        elif in_lifespan_function:
            lifespan_function.append(line)
    
    lifespan_function_text = '\n'.join(lifespan_function)
    
    # Check that process_pdf_if_needed is not called
    if "await process_pdf_if_needed()" in lifespan_function_text and "# Removed:" not in lifespan_function_text:
        print("❌ process_pdf_if_needed is still being called in lifespan function")
        return False
    
    print("✅ lifespan function properly updated - no automatic PDF processing")
    return True

def test_chat_endpoint_requires_uploads():
    """Test that chat endpoint properly checks for uploaded documents."""
    print("\n🧪 Testing chat endpoint requires uploaded documents...")
    
    main_py_path = Path("main.py")
    with open(main_py_path, 'r') as f:
        content = f.read()
    
    # Find the chat endpoint function
    lines = content.split('\n')
    chat_function = []
    in_chat_function = False
    
    for line in lines:
        if "@app.post(\"/chat\"" in line:
            in_chat_function = True
        elif in_chat_function and line.startswith("@app.") and "/chat" not in line:
            break
        elif in_chat_function:
            chat_function.append(line)
    
    chat_function_text = '\n'.join(chat_function)
    
    # Check that it checks for active_temp_database
    if "if active_temp_database is None:" not in chat_function_text:
        print("❌ Chat endpoint doesn't check for uploaded documents")
        return False
    
    # Check that it returns the correct error message
    if "No PDF documents have been uploaded" not in chat_function_text:
        print("❌ Chat endpoint doesn't return correct error message")
        return False
    
    print("✅ Chat endpoint properly requires uploaded documents")
    return True

def main():
    """Main test function."""
    print("🚀 Testing PDF-Only System Implementation")
    print("=" * 50)
    
    tests = [
        ("No automatic PDF processing", test_no_automatic_pdf_processing),
        ("Global variables updated", test_global_variables_updated),
        ("Initialization function updated", test_initialization_function_updated),
        ("Lifespan function updated", test_lifespan_function_updated),
        ("Chat endpoint requires uploads", test_chat_endpoint_requires_uploads)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The system is now truly PDF-only.")
        print("\nKey changes verified:")
        print("• No automatic PDF processing during startup")
        print("• Global variables updated for PDF-only system")
        print("• Initialization only sets up core components")
        print("• Lifespan function doesn't process default PDFs")
        print("• Chat endpoint requires uploaded documents")
        return 0
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    exit(main())
