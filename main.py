"""
PDF Chat System - FastAPI Application
A RAG chatbot for uploaded PDF documents
"""

import os
import logging
import tempfile
import shutil
import uuid
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from dotenv import load_dotenv

from src.pdf_processor import PDFProcessor
from src.text_chunker import TextChunker
from src.vector_db import VectorDatabase
from src.retriever import SemanticRetriever
from src.gemini_client import GeminiClient
from src.config import Config

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global variables for components
vector_db: Optional[VectorDatabase] = None
retriever: Optional[SemanticRetriever] = None
gemini_client: Optional[GeminiClient] = None
system_initialized = False

# Global variables for single active temporary database
# Only one active session at a time
active_temp_database: Optional[Dict[str, Any]] = None
TEMP_SESSION_ID = "active_session"  # Constant session ID


# Pydantic models
class ChatRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=1000, description="User query about uploaded PDF documents")


class ChatResponse(BaseModel):
    response: str = Field(..., description="AI-generated response")


class SystemStatus(BaseModel):
    status: str = Field(..., description="Overall system status")
    components: Dict[str, Any] = Field(..., description="Status of individual components")
    statistics: Dict[str, Any] = Field(default={}, description="System statistics")


class UploadResponse(BaseModel):
    files_processed: int = Field(..., description="Number of PDF files successfully processed")
    total_chunks: int = Field(..., description="Total number of text chunks created")
    processing_errors: List[str] = Field(default=[], description="List of processing errors for failed files")
    database_status: str = Field(..., description="Status of the temporary database")
    index_stats: Dict[str, Any] = Field(default={}, description="Statistics about the created index")
    source_files: List[str] = Field(default=[], description="List of successfully processed source files")





async def initialize_system():
    """Initialize all system components with comprehensive error handling."""
    global vector_db, retriever, gemini_client, system_initialized

    try:
        logger.info("Initializing SKUAST RAG system...")

        # Validate configuration first with error handling
        try:
            config_validation = Config.validate_config()
            if not config_validation['valid']:
                logger.error("Configuration validation failed:")
                for issue in config_validation['issues']:
                    logger.error(f"  - {issue}")
                raise ValueError("Invalid configuration")

            # Log warnings
            for warning in config_validation['warnings']:
                logger.warning(warning)
        except Exception as e:
            logger.error(f"Error during configuration validation: {e}")
            raise ValueError(f"Configuration validation error: {e}")

        # Initialize vector database with error handling
        try:
            logger.info("Initializing vector database...")
            vector_db_config = Config.get_vector_db_config()

            if not Config.GOOGLE_API_KEY:
                raise ValueError("Google API key not configured")

            vector_db = VectorDatabase(
                model_name=vector_db_config['model_name'],
                db_path=vector_db_config['db_path'],
                api_key=Config.GOOGLE_API_KEY
            )
        except Exception as e:
            logger.error(f"Error initializing vector database: {e}")
            raise ValueError(f"Vector database initialization failed: {e}")

        # Try to load existing index with error handling
        try:
            if vector_db.load_index(Config.INDEX_NAME):
                logger.info("Loaded existing FAISS index")
            else:
                logger.info("No existing index found. Will need to process PDF first.")
        except Exception as e:
            logger.warning(f"Error loading existing index: {e}")
            logger.info("Will need to process PDF to create new index.")

        # Initialize retriever with error handling
        try:
            logger.info("Initializing semantic retriever...")
            retriever = SemanticRetriever(vector_db)
        except Exception as e:
            logger.error(f"Error initializing semantic retriever: {e}")
            raise ValueError(f"Semantic retriever initialization failed: {e}")

        # Initialize Gemini client with error handling
        try:
            logger.info("Initializing Gemini client...")
            if not Config.GOOGLE_API_KEY:
                raise ValueError("Google API key not configured")

            gemini_client = GeminiClient(api_key=Config.GOOGLE_API_KEY, model_name=Config.GEMINI_MODEL)

            # Validate Gemini connection
            if not gemini_client.validate_api_key():
                raise ValueError("Failed to validate Google API key")
        except Exception as e:
            logger.error(f"Error initializing Gemini client: {e}")
            raise ValueError(f"Gemini client initialization failed: {e}")
        
        system_initialized = True
        logger.info("System initialization completed successfully")
        
    except Exception as e:
        logger.error(f"System initialization failed: {e}")
        system_initialized = False
        raise


def status_callback(message: str, level: str = "info"):
    """Status callback function for processing updates."""
    if level == "error":
        logger.error(message)
    elif level == "warning":
        logger.warning(message)
    elif level == "success":
        logger.info(f"SUCCESS: {message}")
    else:
        logger.info(message)


def validate_pdf_file(file: UploadFile) -> Dict[str, Any]:
    """
    Validate uploaded PDF file.

    Args:
        file (UploadFile): Uploaded file to validate

    Returns:
        Dict[str, Any]: Validation result with 'valid' boolean and 'error' message if invalid
    """
    # Check file extension
    if not file.filename or not file.filename.lower().endswith('.pdf'):
        return {"valid": False, "error": f"File '{file.filename}' is not a PDF file"}

    # Check MIME type
    if file.content_type and not file.content_type.lower().startswith('application/pdf'):
        return {"valid": False, "error": f"File '{file.filename}' has invalid MIME type: {file.content_type}"}

    # Check file size (10MB limit)
    max_size = 10 * 1024 * 1024  # 10MB in bytes
    if hasattr(file, 'size') and file.size and file.size > max_size:
        return {"valid": False, "error": f"File '{file.filename}' exceeds 10MB size limit"}

    return {"valid": True, "error": None}


def get_temp_db_path() -> str:
    """Get the path for the single temporary database."""
    return "data/temp_uploads/active_session"


async def clear_active_temp_database():
    """
    Clear the currently active temporary database.
    """
    global active_temp_database

    if active_temp_database is not None:
        try:
            # Remove from memory
            active_temp_database = None

            # Remove files
            db_path = get_temp_db_path()
            if Path(db_path).exists():
                shutil.rmtree(db_path)
                logger.info("Cleared active temporary database")
        except Exception as e:
            logger.error(f"Error clearing active temporary database: {e}")


def combine_pdf_contents(pdf_contents: List[Dict[str, str]]) -> str:
    """
    Combine content from multiple PDFs into a single document.

    Args:
        pdf_contents: List of dicts with 'filename' and 'content' keys

    Returns:
        str: Combined markdown content with source file markers
    """
    combined_content = []

    for pdf_info in pdf_contents:
        filename = pdf_info['filename']
        content = pdf_info['content']

        # Add source file marker
        combined_content.append(f"\n\n# Source: {filename}\n\n")
        combined_content.append(content)
        combined_content.append(f"\n\n--- End of {filename} ---\n\n")

    return "".join(combined_content)


async def process_uploaded_pdfs_combined(files: List[UploadFile]) -> Dict[str, Any]:
    """
    Process uploaded PDF files with combined document approach.

    Args:
        files (List[UploadFile]): List of uploaded PDF files

    Returns:
        Dict[str, Any]: Processing results
    """
    temp_dir = Path(get_temp_db_path())
    temp_dir.mkdir(parents=True, exist_ok=True)

    pdf_processor = PDFProcessor()

    pdf_contents = []
    processing_errors = []
    files_processed = 0
    processed_filenames = []

    # Step 1: Extract content from all PDFs
    for file in files:
        try:
            # Save uploaded file temporarily
            temp_file_path = temp_dir / f"temp_{file.filename}"

            with open(temp_file_path, "wb") as temp_file:
                content = await file.read()
                temp_file.write(content)

            # Process PDF
            try:
                markdown_content = pdf_processor.process_pdf(str(temp_file_path))

                if markdown_content and len(markdown_content.strip()) >= 100:
                    pdf_contents.append({
                        'filename': file.filename,
                        'content': markdown_content
                    })
                    files_processed += 1
                    processed_filenames.append(file.filename)
                    logger.info(f"Successfully extracted content from {file.filename}: {len(markdown_content)} characters")
                else:
                    processing_errors.append(f"File '{file.filename}': Insufficient content extracted")

            except Exception as e:
                processing_errors.append(f"File '{file.filename}': {str(e)}")
                logger.error(f"Error processing {file.filename}: {e}")

            # Clean up temporary file
            if temp_file_path.exists():
                temp_file_path.unlink()

        except Exception as e:
            processing_errors.append(f"File '{file.filename}': Failed to save/read file - {str(e)}")
            logger.error(f"Error handling file {file.filename}: {e}")

    # Step 2: Combine all content and chunk as single document
    all_chunks = []
    if pdf_contents:
        try:
            # Combine all PDF contents into single document
            combined_content = combine_pdf_contents(pdf_contents)
            logger.info(f"Combined content from {len(pdf_contents)} files: {len(combined_content)} characters")

            # Chunk the combined content
            chunking_config = Config.get_chunking_config()
            chunker = TextChunker(
                chunk_size=chunking_config['chunk_size'],
                overlap_size=chunking_config['overlap_size']
            )

            chunks = chunker.split_text_into_chunks(combined_content)

            # Add metadata to chunks
            for i, chunk in enumerate(chunks):
                # Extract source files mentioned in this chunk
                chunk_text = chunk['text']
                source_files = []
                for pdf_info in pdf_contents:
                    if pdf_info['filename'] in chunk_text or any(word in chunk_text for word in pdf_info['content'].split()[:10]):
                        source_files.append(pdf_info['filename'])

                # If no specific source detected, assume it could be from any file
                if not source_files:
                    source_files = processed_filenames

                chunk['source_files'] = source_files
                chunk['chunk_type'] = 'combined'
                chunk['total_source_files'] = len(processed_filenames)

            all_chunks = chunks
            logger.info(f"Created {len(chunks)} chunks from combined content")

        except Exception as e:
            processing_errors.append(f"Error combining and chunking content: {str(e)}")
            logger.error(f"Error in combined processing: {e}")

    return {
        "all_chunks": all_chunks,
        "files_processed": files_processed,
        "processing_errors": processing_errors,
        "processed_filenames": processed_filenames
    }





async def process_pdf_if_needed():
    """Process PDF and build index if not already done with comprehensive error handling."""
    global vector_db

    try:
        # Check if processing is needed
        if not vector_db:
            logger.error("Vector database not initialized")
            return

        if vector_db.index is not None:
            logger.info("Index already exists, skipping PDF processing")
            return  # Index already exists

        # Check if PDF file exists
        pdf_path = "skuast.pdf"
        if not Path(pdf_path).exists():
            error_msg = f"PDF file not found: {pdf_path}"
            logger.error(error_msg)
            status_callback(error_msg, "error")
            return

        status_callback("Processing PDF and building vector index...", "info")

        # Process PDF with error handling
        try:
            pdf_processor = PDFProcessor()
            markdown_content = pdf_processor.process_pdf(pdf_path)
        except Exception as e:
            error_msg = f"Error processing PDF file: {e}"
            logger.error(error_msg)
            status_callback(error_msg, "error")
            return

        # Validate PDF content
        if not markdown_content or len(markdown_content.strip()) < 100:
            status_callback("PDF processing resulted in insufficient content", "warning")
            return

        # Chunk text with error handling
        try:
            chunking_config = Config.get_chunking_config()
            chunker = TextChunker(
                chunk_size=chunking_config['chunk_size'],
                overlap_size=chunking_config['overlap_size']
            )
            chunks = chunker.split_text_into_chunks(markdown_content)
        except Exception as e:
            error_msg = f"Error chunking text: {e}"
            logger.error(error_msg)
            status_callback(error_msg, "error")
            return

        # Validate chunks
        if not chunks:
            status_callback("Text chunking resulted in no chunks", "error")
            return

        # Build vector index with error handling
        try:
            build_success = vector_db.build_index(chunks, status_callback=status_callback)
        except Exception as e:
            error_msg = f"Error building vector index: {e}"
            logger.error(error_msg)
            status_callback(error_msg, "error")
            return

        # Save index with error handling
        if build_success:
            try:
                vector_db.save_index("skuast_index")
                status_callback(f"Successfully processed PDF and created index with {len(chunks)} chunks", "success")
            except Exception as e:
                error_msg = f"Error saving index: {e}"
                logger.error(error_msg)
                status_callback(error_msg, "error")
        else:
            status_callback("Failed to build vector index", "error")

    except Exception as e:
        error_msg = f"Unexpected error processing PDF: {e}"
        logger.error(error_msg)
        status_callback(error_msg, "error")


async def periodic_cleanup():
    """Periodic cleanup task for temporary database."""
    while True:
        try:
            # For single session model, we don't need periodic cleanup
            # The active session is managed manually
            await asyncio.sleep(3600)  # Sleep for 1 hour
        except Exception as e:
            logger.error(f"Error in periodic cleanup: {e}")
            await asyncio.sleep(3600)  # Continue running even if there's an error


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan."""
    # Startup
    await initialize_system()
    await process_pdf_if_needed()

    # Start periodic cleanup task
    cleanup_task = asyncio.create_task(periodic_cleanup())

    yield

    # Shutdown
    logger.info("Shutting down SKUAST RAG system")

    # Cancel cleanup task
    cleanup_task.cancel()
    try:
        await cleanup_task
    except asyncio.CancelledError:
        pass

    # Clean up active temporary database on shutdown
    try:
        await clear_active_temp_database()
        logger.info("Cleaned up active temporary database on shutdown")
    except Exception as e:
        logger.error(f"Error during shutdown cleanup: {e}")


# Create FastAPI app
app = FastAPI(
    title="PDF Chat System",
    description="A Retrieval-Augmented Generation chatbot for uploaded PDF documents. Upload your PDFs and chat with their content.",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# @app.get("/", response_model=Dict[str, str])
# async def root():
#     """Root endpoint with basic information."""
#     return {
#         "message": "SKUAST RAG Chatbot API",
#         "description": "Ask questions about Sher-e-Kashmir University of Agricultural Sciences and Technology",
#         "endpoints": {
#             "chat": "POST /chat - Ask questions about SKUAST",
#             "status": "GET /status - Check system status",
#             "health": "GET /health - Health check"
#         }
#     }


@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    Chat with your uploaded PDF documents. Requires PDFs to be uploaded first via /upload-pdfs endpoint.
    """
    global active_temp_database

    try:
        # Check if uploaded documents are available (required for PDF-only system)
        if active_temp_database is None:
            logger.info("Chat request received but no uploaded documents available")
            raise HTTPException(
                status_code=400,
                detail="No PDF documents have been uploaded. Please upload PDF files first using the /upload-pdfs endpoint before chatting."
            )

        # Component availability check (gemini_client is always required)
        if not gemini_client:
            logger.error("Chat request received but Gemini client not available")
            raise HTTPException(
                status_code=503,
                detail="AI service is not available. Please try again later."
            )

        # Input validation
        if not request.query or not request.query.strip():
            logger.warning("Empty query received")
            raise HTTPException(
                status_code=400,
                detail="Query cannot be empty"
            )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error in initial chat validation: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error during request validation"
        )
    
    # Main processing with comprehensive error handling
    try:
        logger.info(f"Processing chat request: '{request.query[:50]}...'")

        # Use hardcoded values for top_k and temperature
        top_k = 3  # Hardcoded default value
        temperature = 0.3  # Hardcoded default value

        # Use uploaded documents database (PDF-only system)
        logger.info("Using uploaded documents database for chat query")
        temp_retriever = active_temp_database["retriever"]

        # Create context using temporary retriever
        try:
            context_data = temp_retriever.create_context_for_llm(
                query=request.query,
                top_k=top_k
            )
        except Exception as e:
            logger.error(f"Error during context retrieval from uploaded documents: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error retrieving relevant information from uploaded documents. Please try again."
            )

        # Check if context was found in uploaded documents
        if not context_data or context_data.get('num_sources', 0) == 0:
            logger.info("No relevant context found in uploaded documents")
            return ChatResponse(
                response="Sorry, I don't have information on this topic in the uploaded documents."
            )
        
        # Generate response using Gemini with comprehensive error handling
        try:
            logger.info("Generating response using Gemini")
            gemini_response = gemini_client.generate_response(
                query=request.query,
                context=context_data['context'],
                temperature=temperature
            )

            # Check if response generation was successful
            if not gemini_response or not gemini_response.get('success', False):
                error_msg = gemini_response.get('error', 'Unknown error') if gemini_response else 'No response received'
                logger.warning(f"Gemini generation failed: {error_msg}")
                return ChatResponse(
                    response="Sorry, I couldn't generate a response at the moment. Please try again."
                )

            # Validate response content
            if not gemini_response.get('response') or not gemini_response['response'].strip():
                logger.warning("Gemini returned empty response")
                return ChatResponse(
                    response="Sorry, I couldn't generate a meaningful response. Please try rephrasing your question."
                )

        except ConnectionError as e:
            logger.error(f"Network connection error during Gemini API call: {e}")
            raise HTTPException(
                status_code=503,
                detail="Service temporarily unavailable. Please try again later."
            )
        except TimeoutError as e:
            logger.error(f"Timeout error during Gemini API call: {e}")
            raise HTTPException(
                status_code=504,
                detail="Request timeout. Please try again."
            )
        except Exception as e:
            logger.error(f"Unexpected error during response generation: {e}")
            return ChatResponse(
                response="Sorry, I encountered an error while processing your question. Please try again."
            )
        
        # Successful response
        return ChatResponse(
            response=gemini_response['response']
        )

    except HTTPException:
        # Re-raise HTTP exceptions (they're already handled)
        raise
    except ValueError as e:
        logger.error(f"Validation error in chat processing: {e}")
        raise HTTPException(
            status_code=400,
            detail="Invalid request data"
        )
    except ConnectionError as e:
        logger.error(f"Connection error in chat processing: {e}")
        raise HTTPException(
            status_code=503,
            detail="Service temporarily unavailable"
        )
    except TimeoutError as e:
        logger.error(f"Timeout error in chat processing: {e}")
        raise HTTPException(
            status_code=504,
            detail="Request timeout"
        )
    except Exception as e:
        logger.error(f"Unexpected error processing chat request: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred. Please try again."
        )


@app.post("/upload-pdfs", response_model=UploadResponse)
async def upload_pdfs(
    files: List[UploadFile] = File(..., description="PDF files to upload and process")
):
    """
    Upload multiple PDF files and process them into a single temporary vector database.
    Replaces any existing temporary database.
    """
    global active_temp_database

    try:
        # Validate number of files
        if len(files) > 5:
            raise HTTPException(
                status_code=400,
                detail="Maximum 5 files allowed per upload"
            )

        if len(files) == 0:
            raise HTTPException(
                status_code=400,
                detail="At least one file must be uploaded"
            )

        # Validate each file
        validation_errors = []
        for file in files:
            validation_result = validate_pdf_file(file)
            if not validation_result["valid"]:
                validation_errors.append(validation_result["error"])

        if validation_errors:
            raise HTTPException(
                status_code=400,
                detail=f"File validation failed: {'; '.join(validation_errors)}"
            )

        # Clear any existing temporary database
        await clear_active_temp_database()

        # Process uploaded PDFs with combined approach
        try:
            processing_result = await process_uploaded_pdfs_combined(files)

            if processing_result["files_processed"] == 0:
                raise HTTPException(
                    status_code=400,
                    detail=f"No files could be processed. Errors: {'; '.join(processing_result['processing_errors'])}"
                )

            # Create temporary vector database
            temp_db_path = get_temp_db_path()
            temp_vector_db = VectorDatabase(
                model_name=Config.EMBEDDING_MODEL,
                db_path=temp_db_path,
                api_key=Config.GOOGLE_API_KEY
            )

            # Build index from combined chunks
            chunks = processing_result["all_chunks"]
            build_success = temp_vector_db.build_index(chunks)

            if not build_success:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to build vector index from uploaded files"
                )

            # Save the temporary index
            temp_vector_db.save_index("temp_index_active")

            # Create temporary retriever
            temp_retriever = SemanticRetriever(temp_vector_db)

            # Store in global variable
            active_temp_database = {
                "vector_db": temp_vector_db,
                "retriever": temp_retriever,
                "created_at": datetime.now(),
                "path": temp_db_path,
                "files_processed": processing_result["files_processed"],
                "total_chunks": len(chunks),
                "source_files": processing_result["processed_filenames"]
            }

            # Get index statistics
            index_stats = temp_vector_db.get_index_stats()

            logger.info(f"Successfully created temporary database with {len(chunks)} chunks from {processing_result['files_processed']} files")

            return UploadResponse(
                files_processed=processing_result["files_processed"],
                total_chunks=len(chunks),
                processing_errors=processing_result["processing_errors"],
                database_status="active",
                index_stats=index_stats,
                source_files=processing_result["processed_filenames"]
            )

        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.error(f"Error processing uploaded files: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Error processing uploaded files: {str(e)}"
            )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error in upload endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred during file upload"
        )








# @app.get("/status", response_model=SystemStatus)
# async def get_status():
#     """Get system status and component information."""
#     try:
#         components = {}
        
#         # Vector database status
#         if vector_db:
#             components["vector_database"] = vector_db.get_index_stats()
#         else:
#             components["vector_database"] = {"status": "Not initialized"}
        
#         # Retriever status
#         if retriever:
#             components["retriever"] = retriever.get_retrieval_stats()
#         else:
#             components["retriever"] = {"status": "Not initialized"}
        
#         # Gemini client status
#         if gemini_client:
#             components["gemini_client"] = gemini_client.get_model_info()
#         else:
#             components["gemini_client"] = {"status": "Not initialized"}
        
#         # Overall status
#         overall_status = "Ready" if system_initialized else "Not Ready"
        
#         # Statistics
#         statistics = {
#             "system_initialized": system_initialized,
#             "pdf_processed": vector_db.index is not None if vector_db else False,
#             "api_key_configured": os.getenv('GOOGLE_API_KEY') is not None
#         }
        
#         return SystemStatus(
#             status=overall_status,
#             components=components,
#             statistics=statistics
#         )
        
#     except Exception as e:
#         logger.error(f"Error getting system status: {e}")
#         raise HTTPException(
#             status_code=500,
#             detail=f"Error retrieving system status: {str(e)}"
#         )


# @app.get("/health")
# async def health_check():
#     """Simple health check endpoint."""
#     return {
#         "status": "healthy" if system_initialized else "unhealthy",
#         "timestamp": "2024-01-01T00:00:00Z"  # You might want to use actual timestamp
#     }


# @app.post("/rebuild-index")
# async def rebuild_index(background_tasks: BackgroundTasks):
    """Rebuild the vector index (admin endpoint)."""
    if not system_initialized:
        raise HTTPException(
            status_code=503,
            detail="System not initialized"
        )
    
    background_tasks.add_task(process_pdf_if_needed)
    
    return {
        "message": "Index rebuild started in background",
        "status": "processing"
    }


if __name__ == "__main__":
    import uvicorn

    # Get API configuration
    api_config = Config.get_api_config()

    # Run the application
    uvicorn.run(
        "main:app",
        host=api_config['host'],
        port=api_config['port'],
        reload=api_config['reload'],
        log_level="info"
    )
