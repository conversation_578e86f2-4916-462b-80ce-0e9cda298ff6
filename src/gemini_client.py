"""
Google Gemini 1.5 Flash integration module.
"""

import os
import logging
from typing import Dict, Any, Optional, List
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GeminiClient:
    """
    A client for interacting with Google Gemini 1.5 Flash model.
    """
    
    def __init__(self, api_key: Optional[str] = None, model_name: str = "gemini-1.5-flash"):
        """
        Initialize the Gemini client.
        
        Args:
            api_key (str, optional): Google API key. If None, will try to get from environment
            model_name (str): Name of the Gemini model to use
        """
        self.model_name = model_name
        
        # Get API key
        if api_key is None:
            api_key = os.getenv('GOOGLE_API_KEY')
        
        if not api_key:
            raise ValueError("Google API key not provided. Set GOOGLE_API_KEY environment variable or pass api_key parameter.")
        
        # Configure the API
        genai.configure(api_key=api_key)
        
        # Initialize the model
        try:
            self.model = genai.GenerativeModel(model_name)
            logger.info(f"Gemini client initialized with model: {model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize Gemini model: {e}")
            raise
        
        # System prompt for SKUAST bot
        self.system_prompt = """You are SKUAST BOT, an AI assistant specialized in providing information about Sher-e-Kashmir University of Agricultural Sciences and Technology (SKUAST).

Your role is to:
1. Answer questions based ONLY on the provided context information
2. Provide accurate, helpful, and detailed responses about SKUAST
3. Respond naturally and conversationally as if the information is part of your own knowledge
4. Always maintain a professional and helpful tone
5. When possible, provide specific details like program names, requirements, procedures, etc.

CRITICAL RESPONSE GUIDELINES:
- NEVER reference "the document," "the provided text," "the text states," "according to the document," "based on the text," or any similar phrases
- NEVER mention that information comes from a document or text source
- Respond as if you naturally know the information about SKUAST
- Present information directly and conversationally
- NEVER make up information that is not in the provided context
- If you're unsure about something, say so clearly without mentioning documents
- If asked about topics not covered in the context, respond with friendly messages like:
  * "I don't have information about that topic"
  * "Sorry, I don't have details on that subject"
  * "That information isn't available in my knowledge base"
  * "I couldn't find information on that topic"

RESPONSE STYLE:
- Be direct and natural in your responses
- Sound like a knowledgeable assistant, not someone reading from a source
- Use conversational language
- Be concise but comprehensive
- Never reveal that you're working with provided context or documents"""
    
    def generate_response(self, query: str, context: str,
                         temperature: float = 0.3,
                         max_tokens: int = 1000) -> Dict[str, Any]:
        """
        Generate a response using Gemini based on the query and context.

        Args:
            query (str): User query
            context (str): Retrieved context from SKUAST document
            temperature (float): Temperature for response generation
            max_tokens (int): Maximum number of tokens in response

        Returns:
            Dict[str, Any]: Response data including text and metadata
        """
        if not query.strip():
            return {
                'response': "Please provide a question about SKUAST.",
                'error': "Empty query",
                'success': False
            }

        if not context.strip():
            return {
                'response': "I don't have any relevant information from the SKUAST document to answer your question. Please try rephrasing your question or ask about topics covered in the SKUAST documentation.",
                'error': "No context provided",
                'success': False
            }

        try:
            # Apply context truncation for better performance
            MAX_CONTEXT_CHAR_LEN = 15000
            original_context_len = len(context)

            if len(context) > MAX_CONTEXT_CHAR_LEN:
                logger.warning(f"Truncating context from {len(context)} to {MAX_CONTEXT_CHAR_LEN} characters")
                context = context[:MAX_CONTEXT_CHAR_LEN] + "\n... (context truncated for length)"

            # Construct the full prompt
            full_prompt = self._construct_prompt(query, context)

            # Configure generation parameters
            generation_config = genai.types.GenerationConfig(
                temperature=temperature,
                max_output_tokens=max_tokens,
                top_p=0.8,
                top_k=40
            )

            # Safety settings
            safety_settings = {
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            }

            # Generate response
            logger.info(f"Generating response for query: '{query[:50]}...'")

            response = self.model.generate_content(
                full_prompt,
                generation_config=generation_config,
                safety_settings=safety_settings
            )

            # Check if response was blocked
            if response.candidates[0].finish_reason.name == "SAFETY":
                return {
                    'response': "I cannot provide a response to this query due to safety considerations. Please rephrase your question.",
                    'error': "Response blocked by safety filters",
                    'success': False
                }

            response_text = response.text.strip()

            logger.info(f"Successfully generated response ({len(response_text)} characters)")

            return {
                'response': response_text,
                'success': True,
                'metadata': {
                    'model': self.model_name,
                    'temperature': temperature,
                    'max_tokens': max_tokens,
                    'prompt_tokens': len(full_prompt.split()),
                    'response_tokens': len(response_text.split()),
                    'finish_reason': response.candidates[0].finish_reason.name,
                    'context_length': original_context_len,
                    'context_truncated': original_context_len > MAX_CONTEXT_CHAR_LEN
                }
            }

        except Exception as e:
            logger.error(f"Error generating response: {e}")

            # Enhanced error handling with specific error types
            error_response = self._handle_generation_error(e)
            return error_response
    
    def _construct_prompt(self, query: str, context: str) -> str:
        """
        Construct the full prompt for Gemini.
        
        Args:
            query (str): User query
            context (str): Retrieved context
            
        Returns:
            str: Full prompt
        """
        prompt = f"""{self.system_prompt}

Context information:
{context}

User Question: {query}

Please provide a helpful and accurate response. If you don't have enough information to fully answer the question, respond naturally with a friendly message indicating you don't have that information."""
        
        return prompt

    def _handle_generation_error(self, error: Exception) -> Dict[str, Any]:
        """
        Handle generation errors with specific error messages.

        Args:
            error (Exception): The exception that occurred

        Returns:
            Dict[str, Any]: Error response dictionary
        """
        error_str = str(error)

        # Check for specific error types
        if "API key not valid" in error_str:
            return {
                'response': "Error: The Gemini API key is not valid. Please check your configuration.",
                'error': "Invalid API key",
                'success': False
            }

        if "400" in error_str and ("userLocation" in error_str or "blocked" in error_str.lower()):
            return {
                'response': "Error: API request blocked, possibly due to safety settings, regional restrictions, or an issue with the query/context provided.",
                'error': "Request blocked",
                'success': False
            }

        if "quota" in error_str.lower() or "limit" in error_str.lower():
            return {
                'response': "Error: API quota exceeded or rate limit reached. Please try again later.",
                'error': "Quota/rate limit exceeded",
                'success': False
            }

        if "timeout" in error_str.lower():
            return {
                'response': "Error: Request timed out. Please try again with a shorter query or context.",
                'error': "Request timeout",
                'success': False
            }

        # Generic error
        return {
            'response': "Sorry, I encountered an error while trying to generate an answer. The context or history might have been too long, or an API issue occurred.",
            'error': error_str,
            'success': False
        }
    
    def chat_with_context(self, messages: List[Dict[str, str]], context: str) -> Dict[str, Any]:
        """
        Handle a conversation with context (for potential future chat functionality).
        
        Args:
            messages (List[Dict[str, str]]): List of message dictionaries with 'role' and 'content'
            context (str): Retrieved context
            
        Returns:
            Dict[str, Any]: Response data
        """
        if not messages:
            return {
                'response': "No messages provided.",
                'error': "Empty messages",
                'success': False
            }
        
        # Get the latest user message
        latest_message = None
        for message in reversed(messages):
            if message.get('role') == 'user':
                latest_message = message.get('content', '')
                break
        
        if not latest_message:
            return {
                'response': "No user message found.",
                'error': "No user message",
                'success': False
            }
        
        # For now, treat it as a single query
        return self.generate_response(latest_message, context)
    
    def validate_api_key(self) -> bool:
        """
        Validate that the API key is working.
        
        Returns:
            bool: True if API key is valid, False otherwise
        """
        try:
            # Try a simple generation to test the API key
            self.model.generate_content("Hello")
            return True
        except Exception as e:
            logger.error(f"API key validation failed: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the current model.
        
        Returns:
            Dict[str, Any]: Model information
        """
        try:
            # Get available models
            models = list(genai.list_models())
            current_model_info = None
            
            for model in models:
                if self.model_name in model.name:
                    current_model_info = {
                        'name': model.name,
                        'display_name': model.display_name,
                        'description': model.description,
                        'input_token_limit': getattr(model, 'input_token_limit', 'Unknown'),
                        'output_token_limit': getattr(model, 'output_token_limit', 'Unknown'),
                        'supported_generation_methods': getattr(model, 'supported_generation_methods', [])
                    }
                    break
            
            return {
                'current_model': current_model_info or {'name': self.model_name, 'status': 'Information not available'},
                'api_status': 'Connected' if self.validate_api_key() else 'Connection failed'
            }
            
        except Exception as e:
            logger.error(f"Error getting model info: {e}")
            return {
                'current_model': {'name': self.model_name, 'status': 'Error retrieving info'},
                'api_status': 'Unknown',
                'error': str(e)
            }


def main():
    """
    Main function for testing the Gemini client.
    """
    try:
        # Initialize client (make sure to set GOOGLE_API_KEY environment variable)
        client = GeminiClient()
        
        # Test query and context
        test_query = "What programs does SKUAST offer?"
        test_context = """
        SKUAST offers various undergraduate and postgraduate programs in agricultural sciences.
        The university provides degrees in agronomy, horticulture, plant pathology, soil science,
        and agricultural engineering. Students can pursue B.Sc., M.Sc., and Ph.D. programs.
        """
        
        # Generate response
        result = client.generate_response(test_query, test_context)
        
        print(f"Query: {test_query}")
        print(f"Success: {result['success']}")
        print(f"Response: {result['response']}")
        
        if 'metadata' in result:
            print(f"Metadata: {result['metadata']}")
        
        # Get model info
        model_info = client.get_model_info()
        print(f"\nModel Info: {model_info}")
        
    except Exception as e:
        print(f"Error testing Gemini client: {e}")
        print("Make sure to set the GOOGLE_API_KEY environment variable")


if __name__ == "__main__":
    main()
