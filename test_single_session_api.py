#!/usr/bin/env python3
"""
Test script for the single-session PDF upload API endpoints.
Tests the API functionality without requiring actual PDF files.
"""

import requests
import json


def test_api_endpoints(base_url="http://localhost:8000"):
    """Test the single-session API endpoints."""
    
    print("🚀 Single-Session API Endpoints Test")
    print("=" * 60)
    
    # Test 1: Check initial status
    print("📊 Test 1: Checking initial status...")
    
    try:
        status_response = requests.get(f"{base_url}/uploaded-docs-status")
        if status_response.status_code == 200:
            status_result = status_response.json()
            print(f"   ✅ Status endpoint working")
            print(f"   Initial status: {status_result['status']}")
            print(f"   Database active: {status_result['database_active']}")
        else:
            print(f"   ❌ Status endpoint failed: {status_response.status_code}")
    except Exception as e:
        print(f"   ❌ Status endpoint error: {e}")
    
    # Test 2: Query without documents
    print("\n🔍 Test 2: Query without uploaded documents...")
    
    try:
        query_response = requests.post(
            f"{base_url}/query-uploaded-docs",
            json={"query": "What is this about?"}
        )
        
        if query_response.status_code == 404:
            print("   ✅ Query without documents correctly returns 404")
            error_detail = query_response.json().get('detail', '')
            print(f"   Error message: {error_detail}")
        else:
            print(f"   ❌ Expected 404, got {query_response.status_code}")
    except Exception as e:
        print(f"   ❌ Query endpoint error: {e}")
    
    # Test 3: Clear when no documents
    print("\n🧹 Test 3: Clear when no documents...")
    
    try:
        clear_response = requests.delete(f"{base_url}/clear-uploaded-docs")
        
        if clear_response.status_code == 404:
            print("   ✅ Clear without documents correctly returns 404")
            error_detail = clear_response.json().get('detail', '')
            print(f"   Error message: {error_detail}")
        else:
            print(f"   ❌ Expected 404, got {clear_response.status_code}")
    except Exception as e:
        print(f"   ❌ Clear endpoint error: {e}")
    
    # Test 4: Upload validation errors
    print("\n📤 Test 4: Upload validation errors...")
    
    # Test empty upload
    try:
        empty_response = requests.post(f"{base_url}/upload-pdfs", files=[])
        
        if empty_response.status_code == 400:
            print("   ✅ Empty upload correctly rejected")
        else:
            print(f"   ❌ Expected 400 for empty upload, got {empty_response.status_code}")
    except Exception as e:
        print(f"   ❌ Empty upload test error: {e}")
    
    # Test 5: API documentation
    print("\n📚 Test 5: API documentation...")
    
    try:
        docs_response = requests.get(f"{base_url}/docs")
        
        if docs_response.status_code == 200:
            print("   ✅ API documentation accessible")
        else:
            print(f"   ❌ API docs failed: {docs_response.status_code}")
    except Exception as e:
        print(f"   ❌ API docs error: {e}")
    
    # Test 6: Main chat endpoint (should still work)
    print("\n💬 Test 6: Main chat endpoint...")
    
    try:
        chat_response = requests.post(
            f"{base_url}/chat",
            json={"query": "What is SKUAST?"}
        )
        
        if chat_response.status_code == 200:
            print("   ✅ Main chat endpoint working")
            response_text = chat_response.json().get('response', '')
            print(f"   Response preview: {response_text[:100]}...")
        else:
            print(f"   ❌ Chat endpoint failed: {chat_response.status_code}")
    except Exception as e:
        print(f"   ❌ Chat endpoint error: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 API Endpoints Summary:")
    print("✅ Single-session model implemented")
    print("✅ No session ID management required")
    print("✅ Proper error handling for missing documents")
    print("✅ Status endpoint for checking database state")
    print("✅ Clear endpoint for manual cleanup")
    print("✅ Main SKUAST chat functionality preserved")
    
    return True


def show_new_endpoints(base_url="http://localhost:8000"):
    """Show the new single-session endpoints."""
    
    print("📚 Single-Session API Endpoints")
    print("=" * 50)
    
    endpoints = [
        {
            "method": "POST",
            "path": "/upload-pdfs",
            "description": "Upload PDF files and create/replace active database",
            "notes": "Replaces any existing temporary database"
        },
        {
            "method": "POST", 
            "path": "/query-uploaded-docs",
            "description": "Query the active temporary database",
            "notes": "No session ID required"
        },
        {
            "method": "GET",
            "path": "/uploaded-docs-status",
            "description": "Get status of uploaded documents",
            "notes": "Check if documents are available for querying"
        },
        {
            "method": "DELETE",
            "path": "/clear-uploaded-docs",
            "description": "Clear the active temporary database",
            "notes": "Manual cleanup option"
        }
    ]
    
    for endpoint in endpoints:
        print(f"\n{endpoint['method']} {endpoint['path']}")
        print(f"   Description: {endpoint['description']}")
        print(f"   Notes: {endpoint['notes']}")
    
    print(f"\n📖 Full API Documentation: {base_url}/docs")
    
    print("\n🔄 Key Changes from Multi-Session Model:")
    print("• No session IDs to manage")
    print("• Single active database at any time")
    print("• New uploads automatically replace existing database")
    print("• Combined document processing for unified knowledge base")
    print("• Simplified API with fewer endpoints")


def main():
    """Main test function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test the single-session PDF upload API")
    parser.add_argument("--url", default="http://localhost:8000", 
                       help="Base URL of the API server")
    parser.add_argument("--show-endpoints", action="store_true",
                       help="Show endpoint information")
    
    args = parser.parse_args()
    
    if args.show_endpoints:
        show_new_endpoints(args.url)
        return 0
    
    # Check if server is running
    try:
        response = requests.get(f"{args.url}/docs", timeout=5)
        if response.status_code != 200:
            print(f"❌ Server not accessible at {args.url}")
            print("Make sure the server is running: python main.py")
            return 1
    except requests.exceptions.RequestException:
        print(f"❌ Cannot connect to server at {args.url}")
        print("Make sure the server is running: python main.py")
        return 1
    
    # Run the tests
    success = test_api_endpoints(args.url)
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Single-session API tests completed successfully!")
        print("\nTo test with actual PDF files:")
        print("1. Place PDF files in the current directory")
        print("2. Run: python demo_pdf_upload.py")
    else:
        print("❌ Some API tests failed.")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
