# SKUAST RAG Chatbot System

A comprehensive Retrieval-Augmented Generation (RAG) chatbot system that answers questions based on the SKUAST document using Google's Gemini 1.5 Flash model.

## Features

- **PDF Processing**: Automatic text extraction and markdown conversion from `skuast.pdf`
- **Intelligent Chunking**: Text splitting with 5000-character chunks and 1000-character overlap
- **Vector Search**: FAISS-powered semantic search with sentence-transformers embeddings
- **AI Integration**: Google Gemini 1.5 Flash for contextual response generation
- **REST API**: FastAPI-based API with comprehensive endpoints
- **Real-time Processing**: Efficient query processing and response generation
- **Error Handling**: Comprehensive error handling and logging
- **System Monitoring**: Status endpoints for system health monitoring

## Quick Start

### Prerequisites

- Python 3.8 or higher
- Google API key for Gemini 1.5 Flash
- `skuast.pdf` file in the project root

### Installation

1. **<PERSON><PERSON> and navigate to the project directory**
```bash
cd skuast
```

2. **Install dependencies**
```bash
pip3 install -r requirements.txt
```

3. **Set up environment variables**
```bash
cp .env.example .env
```
Edit `.env` and add your Google API key:
```
GOOGLE_API_KEY=your_google_gemini_api_key_here
```

4. **Run the application**
```bash
python3 main.py
```

The API will be available at `http://localhost:8000`

### Getting Your Google API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key and add it to your `.env` file

## API Documentation

### Interactive Documentation
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

### Endpoints

#### 1. Chat Endpoint
**POST** `/chat`

Ask questions about SKUAST or uploaded documents and get AI-generated responses.
This endpoint automatically uses uploaded documents when available, otherwise uses the main SKUAST database.

**Request Body:**
```json
{
  "query": "What programs does SKUAST offer?"
}
```

**Response:**
```json
{
  "response": "SKUAST offers various undergraduate and postgraduate programs..."
}
```

#### 2. Upload PDFs Endpoint
**POST** `/upload-pdfs`

Upload PDF files to create a temporary database for querying. The chat endpoint will automatically use these uploaded documents for subsequent queries.

**Request:** Multipart form data with PDF files

**Response:**
```json
{
  "files_processed": 2,
  "total_chunks": 45,
  "processing_errors": [],
  "database_status": "active",
  "index_stats": {
    "total_vectors": 45,
    "model_name": "models/text-embedding-004"
  },
  "source_files": ["document1.pdf", "document2.pdf"]
}
```

#### 2. System Status
**GET** `/status`

Get detailed system status and component information.

**Response:**
```json
{
  "status": "Ready",
  "components": {
    "vector_database": {
      "status": "Index loaded",
      "total_vectors": 150,
      "model_name": "all-MiniLM-L6-v2"
    },
    "retriever": {
      "retriever_status": "Ready",
      "min_score_threshold": 0.1
    },
    "gemini_client": {
      "api_status": "Connected",
      "current_model": {
        "name": "gemini-1.5-flash"
      }
    }
  },
  "statistics": {
    "system_initialized": true,
    "pdf_processed": true,
    "api_key_configured": true
  }
}
```

#### 3. Health Check
**GET** `/health`

Simple health check endpoint.

#### 4. Root Information
**GET** `/`

Basic API information and available endpoints.

## System Architecture

### 1. Document Processing Pipeline
```
skuast.pdf → PDF Processor → Clean Text → Markdown Format
```

### 2. Text Chunking System
```
Markdown Text → Text Chunker → 5000-char chunks (1000-char overlap)
```

### 3. Vector Database Creation
```
Text Chunks → Sentence Transformers → Embeddings → FAISS Index
```

### 4. Query Processing Flow
```
User Query → Semantic Search → Top 3 Chunks → Context Creation
```

### 5. Response Generation
```
Query + Context → Gemini 1.5 Flash → AI Response
```

## Project Structure

```
skuast/
├── main.py                 # FastAPI application entry point
├── src/                    # Source code modules
│   ├── __init__.py
│   ├── pdf_processor.py    # PDF text extraction and cleaning
│   ├── text_chunker.py     # Text chunking with overlap
│   ├── vector_db.py        # FAISS vector database operations
│   ├── retriever.py        # Semantic search and retrieval
│   └── gemini_client.py    # Google Gemini API integration
├── data/                   # Data storage
│   └── vector_db/          # FAISS database files
├── skuast.pdf             # Source PDF document
├── requirements.txt        # Python dependencies
├── .env.example           # Environment variables template
├── .env                   # Environment variables (create this)
└── README.md              # This file
```

## Configuration Options

### Environment Variables
- `GOOGLE_API_KEY`: Your Google Gemini API key (required)

### Chunking Parameters
- **Chunk Size**: 5000 characters (configurable in `TextChunker`)
- **Overlap Size**: 1000 characters (configurable in `TextChunker`)

### Retrieval Parameters
- **Top K**: Number of chunks to retrieve (default: 3, max: 10)
- **Score Threshold**: Minimum similarity score (default: 0.1)

### Generation Parameters
- **Temperature**: Response creativity (0.0-1.0, default: 0.3)
- **Max Tokens**: Maximum response length (default: 1000)

## Usage Examples

### Basic Query (Main SKUAST Database)
```bash
curl -X POST "http://localhost:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{"query": "What is SKUAST?"}'
```

### Upload PDF Documents
```bash
curl -X POST "http://localhost:8000/upload-pdfs" \
     -F "files=@document1.pdf" \
     -F "files=@document2.pdf"
```

### Query Uploaded Documents
After uploading PDFs, the chat endpoint automatically uses them:
```bash
curl -X POST "http://localhost:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{"query": "What information is in the uploaded documents?"}'
```

### Check System Status
```bash
curl -X GET "http://localhost:8000/status"
```

## Troubleshooting

### Common Issues

1. **"Google API key not configured"**
   - Ensure `GOOGLE_API_KEY` is set in your `.env` file
   - Verify the API key is valid and has Gemini access

2. **"PDF file not found"**
   - Ensure `skuast.pdf` is in the project root directory
   - Check file permissions

3. **"No relevant information found"**
   - Try rephrasing your question
   - Ensure your question is related to SKUAST content

4. **Installation Issues**
   - Use Python 3.8 or higher
   - Try installing dependencies individually if batch install fails

### Logs and Debugging

The application provides detailed logging. Check the console output for:
- System initialization status
- PDF processing progress
- Query processing information
- Error messages and stack traces

## Performance Considerations

- **First Run**: Initial PDF processing may take a few minutes
- **Subsequent Runs**: Vector index is cached for faster startup
- **Memory Usage**: Approximately 500MB-1GB depending on document size
- **Response Time**: Typically 2-5 seconds per query

## Security Notes

- Keep your Google API key secure and never commit it to version control
- The API currently allows all origins (CORS) - configure appropriately for production
- Consider implementing rate limiting for production deployments

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is for educational and research purposes.
