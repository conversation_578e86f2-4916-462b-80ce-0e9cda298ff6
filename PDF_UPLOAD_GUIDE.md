# PDF-Only Chat System Guide

## Overview

The PDF Chat System is a document-focused RAG system that requires users to upload their own PDF files before chatting. This system uses a **single-session approach** with **combined document processing** to create a unified knowledge base from all uploaded documents.

**Important**: This system does NOT have a fallback database - PDF uploads are required for all chat functionality.

## Key Features

### 🔄 **Multi-File PDF Processing with Combined Approach**
- Upload up to 5 PDF files simultaneously
- **Combined document processing**: All PDFs are merged into a single document corpus before chunking
- Automatic text extraction and markdown conversion
- Intelligent text chunking across the entire combined content
- File validation (PDF format, size limits)

### 🗄️ **Single Active Temporary Database**
- **One active session at a time**: New uploads replace the previous database
- Google embeddings for semantic search
- No session ID management required
- Automatic replacement when new files are uploaded

### 🔍 **Natural Language Querying**
- Query uploaded documents using natural language
- Same simplified API as main chat endpoint
- Context-aware responses based on the unified document corpus
- No technical document references in responses

### 🧹 **Simplified Session Management**
- Single active database (no session IDs to track)
- Manual cleanup option
- Automatic replacement on new uploads
- Complete cleanup on server shutdown

## API Endpoints

### 1. Upload PDFs

**Endpoint:** `POST /upload-pdfs`

**Description:** Upload multiple PDF files and create a temporary vector database.

**Request:**
- Content-Type: `multipart/form-data`
- Files: Up to 5 PDF files (max 10MB each)

**Example:**
```bash
curl -X POST \
  -F "files=@document1.pdf" \
  -F "files=@document2.pdf" \
  http://localhost:8000/upload-pdfs
```

**Response:**
```json
{
  "files_processed": 2,
  "total_chunks": 45,
  "processing_errors": [],
  "database_status": "active",
  "index_stats": {
    "total_vectors": 45,
    "embedding_dimension": 768,
    "index_type": "IndexFlatIP"
  },
  "source_files": ["document1.pdf", "document2.pdf"]
}
```

### 2. Query Uploaded Documents

**Endpoint:** `POST /chat`

**Description:** The main chat endpoint automatically uses uploaded documents when available. No separate endpoint needed!

**Request:**
```json
{
  "query": "What is the main topic of the uploaded documents?"
}
```

**Example:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query": "What farming techniques are mentioned?"}' \
  http://localhost:8000/chat
```

**Response:**
```json
{
  "response": "The documents discuss several farming techniques including crop rotation, organic fertilization, and sustainable irrigation methods."
}
```

**Note:** The `/chat` endpoint requires uploaded documents. If no documents are uploaded, it will return an error asking users to upload PDFs first.

## File Validation Rules

### **Supported Formats**
- PDF files only (`.pdf` extension)
- MIME type: `application/pdf`

### **Size Limits**
- Maximum file size: 10MB per file
- Maximum files per upload: 5 files
- Total upload size: 50MB maximum

### **Content Requirements**
- Files must contain extractable text
- Minimum content length: 100 characters after extraction
- Password-protected PDFs are not supported

## Error Handling

### **Common Error Scenarios**

1. **File Validation Errors (400)**
   ```json
   {
     "detail": "File validation failed: File 'document.txt' is not a PDF file"
   }
   ```

2. **Too Many Files (400)**
   ```json
   {
     "detail": "Maximum 5 files allowed per upload"
   }
   ```

3. **Processing Errors (400)**
   ```json
   {
     "detail": "No files could be processed. Errors: File 'corrupted.pdf': Failed to extract text"
   }
   ```

4. **No Documents Uploaded (400)**
   When no documents are uploaded, the chat endpoint returns an error:
   ```json
   {
     "detail": "No PDF documents have been uploaded. Please upload PDF files first using the /upload-pdfs endpoint before chatting."
   }
   ```

5. **Server Errors (500)**
   ```json
   {
     "detail": "Failed to build vector index from uploaded files"
   }
   ```

## Usage Examples

### **Python Example**
```python
import requests

# Upload files (replaces any existing database)
files = [
    ('files', ('doc1.pdf', open('doc1.pdf', 'rb'), 'application/pdf')),
    ('files', ('doc2.pdf', open('doc2.pdf', 'rb'), 'application/pdf'))
]

response = requests.post('http://localhost:8000/upload-pdfs', files=files)
result = response.json()

print(f"Files processed: {result['files_processed']}")
print(f"Total chunks: {result['total_chunks']}")
print(f"Source files: {result['source_files']}")

# Query the uploaded documents using the main chat endpoint
query_response = requests.post(
    'http://localhost:8000/chat',
    json={'query': 'What is this document about?'}
)
answer = query_response.json()['response']
print(f"Answer: {answer}")

# Note: The chat endpoint automatically uses uploaded documents when available
# No separate status check or cleanup endpoints needed
```

### **JavaScript Example**
```javascript
// Upload files (replaces any existing database)
const formData = new FormData();
formData.append('files', file1);
formData.append('files', file2);

const uploadResponse = await fetch('/upload-pdfs', {
    method: 'POST',
    body: formData
});
const uploadResult = await uploadResponse.json();

console.log(`Files processed: ${uploadResult.files_processed}`);
console.log(`Total chunks: ${uploadResult.total_chunks}`);
console.log(`Source files: ${uploadResult.source_files}`);

// Query the uploaded documents using the main chat endpoint
const queryResponse = await fetch('/chat', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ query: 'What is this about?' })
});
const queryResult = await queryResponse.json();
console.log(`Answer: ${queryResult.response}`);

// Note: The chat endpoint automatically uses uploaded documents when available
// No separate status check or cleanup endpoints needed
```

## Single-Session Management

### **Active Database Model**
- **Single Active Session:** Only one temporary database exists at any time
- **No Session IDs:** No need to track or manage session identifiers
- **Automatic Replacement:** New uploads automatically replace the existing database

### **Database Lifecycle**
1. **Creation:** When PDFs are uploaded successfully (replaces any existing database)
2. **Active:** Available for querying until replaced or manually cleared
3. **Replacement:** Automatic when new files are uploaded
4. **Manual Cleanup:** Available via `/clear-uploaded-docs` endpoint

### **Combined Document Processing**
- **Unified Corpus:** All uploaded PDFs are merged into a single document before chunking
- **Cross-File Chunks:** Text chunks can span across multiple source files
- **Source Tracking:** Metadata tracks which files contributed to each chunk
- **Improved Retrieval:** Better context understanding across the entire document set

## Performance Considerations

### **Upload Performance**
- Large files take longer to process
- Multiple files are processed sequentially
- Embedding generation may take time for large documents

### **Query Performance**
- First query may be slower (index loading)
- Subsequent queries are faster (cached in memory)
- Response time depends on document size and complexity

### **Memory Usage**
- Each session maintains vector database in memory
- Automatic cleanup prevents memory leaks
- Monitor server resources with many concurrent sessions

## Security Considerations

### **File Security**
- Uploaded files are stored temporarily
- Files are deleted after session cleanup
- No persistent storage of user documents

### **Session Security**
- Session IDs are randomly generated
- No authentication required (consider adding for production)
- Sessions are isolated from each other

### **Content Privacy**
- Documents are processed locally
- No data sent to external services except Google embeddings API
- Temporary storage only

## Testing

### **Run Tests**
```bash
# Basic functionality test
python test_pdf_upload_system.py

# Include error scenario tests
python test_pdf_upload_system.py --test-errors

# Interactive demo
python demo_pdf_upload.py

# Show API endpoints
python demo_pdf_upload.py --show-endpoints
```

### **Manual Testing**
1. Upload PDF files using the `/upload-pdfs` endpoint
2. Query the temporary database using the session ID
3. Test error scenarios (invalid files, large files, etc.)
4. Verify cleanup functionality

## Troubleshooting

### **Common Issues**

1. **"No files could be processed"**
   - Check if PDFs contain extractable text
   - Verify files are not corrupted or password-protected
   - Ensure files meet size requirements

2. **"No PDF documents have been uploaded" error**
   - Upload documents first using `/upload-pdfs` endpoint
   - Ensure PDFs contain extractable text and meet size requirements

3. **"Failed to build vector index"**
   - Google API quota may be exceeded
   - Check Google API key configuration
   - Verify network connectivity

4. **Slow processing**
   - Large files take more time
   - Google API rate limiting may apply
   - Server resources may be limited

### **Debugging**
- Check server logs for detailed error messages
- Monitor API quota usage in Google Cloud Console
- Verify file permissions for temporary directories
- Test with smaller files first

## System Architecture

### **PDF-Only Design**
- No main database - system only works with uploaded documents
- Chat functionality requires PDF uploads
- Single active session model

### **Shared Components**
- Uses same PDF processing pipeline
- Same text chunking configuration
- Same Google embeddings API
- Same Gemini LLM for responses

### **Configuration**
- Inherits settings from main configuration
- Uses same API keys and credentials
- Follows same error handling patterns
